import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { EncryptionService } from '../common/services/encryption.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly encryptionService: EncryptionService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    const userData = { ...createUserDto };

    // Encrypt CTM password if provided
    if (userData.ctmPassword) {
      userData.ctmPassword = this.encryptionService.encrypt(userData.ctmPassword);
    }

    // Encrypt SeqRNG API token if provided
    if (userData.seqrngApiToken) {
      userData.seqrngApiToken = this.encryptionService.encrypt(userData.seqrngApiToken);
    }

    const user = this.userRepository.create(userData);
    return this.userRepository.save(user);
  }

  async findAll(): Promise<User[]> {
    return this.userRepository.find({
      select: ['id', 'email', 'firstName', 'lastName', 'company', 'role', 'isActive', 'createdAt', 'updatedAt'],
    });
  }

  async findAllForAdmin(): Promise<User[]> {
    return this.userRepository.find({
      select: [
        'id',
        'email',
        'firstName',
        'lastName',
        'company',
        'role',
        'isActive',
        'createdAt',
        'updatedAt',
        // CTM Configuration fields
        'ctmIpAddress',
        'ctmUsername',
        'ctmDomain',
        // SeqRNG Configuration fields
        'seqrngIpAddress',
        // Note: ctmPassword and seqrngApiToken are excluded for security
      ],
    });
  }

  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      select: ['id', 'email', 'firstName', 'lastName', 'company', 'role', 'isActive', 'createdAt', 'updatedAt'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findOneForAdmin(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      select: [
        'id',
        'email',
        'firstName',
        'lastName',
        'company',
        'role',
        'isActive',
        'createdAt',
        'updatedAt',
        // CTM Configuration fields
        'ctmIpAddress',
        'ctmUsername',
        'ctmDomain',
        // SeqRNG Configuration fields
        'seqrngIpAddress',
        // Note: ctmPassword and seqrngApiToken are excluded for security
      ],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email },
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    // First, get the current user with all fields including encrypted ones
    const user = await this.userRepository.findOne({
      where: { id },
      select: [
        'id', 'email', 'firstName', 'lastName', 'company', 'role', 'isActive',
        'ctmIpAddress', 'ctmUsername', 'ctmPassword', 'ctmDomain',
        'seqrngIpAddress', 'seqrngApiToken', 'createdAt', 'updatedAt'
      ],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.userRepository.findOne({
        where: { email: updateUserDto.email },
      });

      if (existingUser) {
        throw new ConflictException('Email already exists');
      }
    }

    const updateData = { ...updateUserDto };

    // Remove null values for sensitive fields to preserve existing values
    if (updateData.ctmPassword === null) {
      delete updateData.ctmPassword;
    } else if (updateData.ctmPassword) {
      // Encrypt CTM password if provided and not null
      updateData.ctmPassword = this.encryptionService.encrypt(updateData.ctmPassword);
    }

    if (updateData.seqrngApiToken === null) {
      delete updateData.seqrngApiToken;
    } else if (updateData.seqrngApiToken) {
      // Encrypt SeqRNG API token if provided and not null
      updateData.seqrngApiToken = this.encryptionService.encrypt(updateData.seqrngApiToken);
    }

    Object.assign(user, updateData);
    return this.userRepository.save(user);
  }

  async updateRefreshToken(userId: string, refreshToken: string | null): Promise<void> {
    await this.userRepository.update(userId, { refreshToken: refreshToken });
  }

  async findByIdWithPassword(id: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { id },
      select: [
        'id', 'email', 'firstName', 'lastName', 'company', 'role', 'isActive',
        'password', 'createdAt', 'updatedAt'
      ],
    });
  }

  async changePassword(userId: string, newPassword: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Actualizar la contraseña (se hasheará automáticamente por el hook @BeforeUpdate)
    user.password = newPassword;
    await this.userRepository.save(user);
  }

  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    await this.userRepository.remove(user);
  }


}
