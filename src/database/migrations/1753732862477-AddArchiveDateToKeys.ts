import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddArchiveDateToKeys1753732862477 implements MigrationInterface {
  name = 'AddArchiveDateToKeys1753732862477';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add archiveDate column to keys table
    await queryRunner.addColumn(
      'keys',
      new TableColumn({
        name: 'archiveDate',
        type: 'timestamp',
        isNullable: true,
        comment: 'Date when the key was archived in CTM',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove archiveDate column from keys table
    await queryRunner.dropColumn('keys', 'archiveDate');
  }
}
