import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateUserCtmData1754000003000 implements MigrationInterface {
  name = 'MigrateUserCtmData1754000003000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Get all users with CTM configuration
    const usersWithCtm = await queryRunner.query(`
      SELECT id, "firstName", "lastName", company, "ctmIpAddress", "ctmUsername", "ctmPassword", "ctmDomain", "seqrngIpAddress", "seqrngApiToken"
      FROM users 
      WHERE "ctmIpAddress" IS NOT NULL 
        AND "ctmUsername" IS NOT NULL 
        AND "ctmPassword" IS NOT NULL 
        AND "ctmDomain" IS NOT NULL
    `);

    for (const user of usersWithCtm) {
      // Generate CTM name based on user info
      let ctmName: string;
      if (user.company) {
        // Convert company name to lowercase and replace spaces with underscores
        ctmName = user.company.toLowerCase().replace(/\s+/g, '_');
      } else {
        // Use firstName_lastName format
        ctmName = `${user.firstName}_${user.lastName}`.toLowerCase();
      }

      // Ensure CTM name is unique by adding suffix if needed
      let finalCtmName = ctmName;
      let counter = 1;
      while (true) {
        const existingCtm = await queryRunner.query(
          'SELECT id FROM ctms WHERE name = $1',
          [finalCtmName]
        );
        if (existingCtm.length === 0) {
          break;
        }
        finalCtmName = `${ctmName}_${counter}`;
        counter++;
      }

      // Create CTM record
      const ctmResult = await queryRunner.query(`
        INSERT INTO ctms (id, name, description, "ipAddress", username, password, domain, "seqrngIpAddress", "seqrngApiToken", "isActive", "createdAt", "updatedAt")
        VALUES (uuid_generate_v4(), $1, $2, $3, $4, $5, $6, $7, $8, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `, [
        finalCtmName,
        `CTM migrado desde usuario ${user.firstName} ${user.lastName}`,
        user.ctmIpAddress,
        user.ctmUsername,
        user.ctmPassword,
        user.ctmDomain,
        user.seqrngIpAddress,
        user.seqrngApiToken
      ]);

      const ctmId = ctmResult[0].id;

      // Assign CTM to user
      await queryRunner.query(`
        INSERT INTO user_ctms ("userId", "ctmId", "assignedAt")
        VALUES ($1, $2, CURRENT_TIMESTAMP)
      `, [user.id, ctmId]);

      // Update all keys from this user to reference the new CTM
      await queryRunner.query(`
        UPDATE keys 
        SET "ctmId" = $1 
        WHERE "userId" = $2
      `, [ctmId, user.id]);
    }

    console.log(`Migrated ${usersWithCtm.length} users with CTM configurations`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This migration is not easily reversible since we're moving data
    // We would need to restore CTM configurations back to users table
    // For now, we'll just clear the new tables
    
    await queryRunner.query('DELETE FROM user_ctms');
    await queryRunner.query('UPDATE keys SET "ctmId" = NULL');
    await queryRunner.query('DELETE FROM ctms');
    
    console.log('Rolled back CTM data migration');
  }
}
