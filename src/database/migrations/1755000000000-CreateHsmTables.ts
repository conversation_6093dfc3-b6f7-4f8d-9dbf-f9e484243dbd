import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeign<PERSON>ey } from 'typeorm';

export class CreateHsmTables1755000000000 implements MigrationInterface {
  name = 'CreateHsmTables1755000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create HSM key type enums
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "hsm_key_type_enum" AS ENUM('aes', 'rsa', 'ec', 'kyberkn', 'dilithiumkn');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "ecc_curve_type_enum" AS ENUM('sect233k1', 'secp256r1', 'secp384r1', 'secp521r1');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "hsm_key_status_enum" AS ENUM('pending', 'created', 'failed');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create HSMs table
    await queryRunner.createTable(
      new Table({
        name: 'hsms',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'url',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create HSM keys table
    await queryRunner.createTable(
      new Table({
        name: 'hsm_keys',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'keyName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'keyType',
            type: 'enum',
            enum: ['aes', 'rsa', 'ec', 'kyberkn', 'dilithiumkn'],
            isNullable: false,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['pending', 'created', 'failed'],
            default: "'pending'",
            isNullable: false,
          },
          {
            name: 'keySize',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'eccCurveType',
            type: 'enum',
            enum: ['sect233k1', 'secp256r1', 'secp384r1', 'secp521r1'],
            isNullable: true,
          },
          {
            name: 'hsmKeyId',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'hsmResponse',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'errorMessage',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'owner',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'userId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'hsmId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create user_hsms junction table
    await queryRunner.createTable(
      new Table({
        name: 'user_hsms',
        columns: [
          {
            name: 'userId',
            type: 'uuid',
            isPrimary: true,
          },
          {
            name: 'hsmId',
            type: 'uuid',
            isPrimary: true,
          },
          {
            name: 'assignedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create indexes for HSMs table
    await queryRunner.createIndex(
      'hsms',
      new TableIndex({
        name: 'IDX_HSM_NAME',
        columnNames: ['name'],
      }),
    );

    await queryRunner.createIndex(
      'hsms',
      new TableIndex({
        name: 'IDX_HSM_ACTIVE',
        columnNames: ['isActive'],
      }),
    );

    // Create indexes for HSM keys table
    await queryRunner.createIndex(
      'hsm_keys',
      new TableIndex({
        name: 'IDX_HSM_KEY_NAME',
        columnNames: ['keyName'],
      }),
    );

    await queryRunner.createIndex(
      'hsm_keys',
      new TableIndex({
        name: 'IDX_HSM_KEY_TYPE',
        columnNames: ['keyType'],
      }),
    );

    await queryRunner.createIndex(
      'hsm_keys',
      new TableIndex({
        name: 'IDX_HSM_KEY_STATUS',
        columnNames: ['status'],
      }),
    );

    await queryRunner.createIndex(
      'hsm_keys',
      new TableIndex({
        name: 'IDX_HSM_KEY_USER',
        columnNames: ['userId'],
      }),
    );

    await queryRunner.createIndex(
      'hsm_keys',
      new TableIndex({
        name: 'IDX_HSM_KEY_HSM',
        columnNames: ['hsmId'],
      }),
    );

    // Create foreign keys
    await queryRunner.createForeignKey(
      'hsm_keys',
      new TableForeignKey({
        columnNames: ['userId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'hsm_keys',
      new TableForeignKey({
        columnNames: ['hsmId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'hsms',
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'user_hsms',
      new TableForeignKey({
        columnNames: ['userId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'user_hsms',
      new TableForeignKey({
        columnNames: ['hsmId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'hsms',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys
    const userHsmsTable = await queryRunner.getTable('user_hsms');
    const hsmKeysTable = await queryRunner.getTable('hsm_keys');
    
    if (userHsmsTable) {
      const userHsmsForeignKeys = userHsmsTable.foreignKeys;
      for (const foreignKey of userHsmsForeignKeys) {
        await queryRunner.dropForeignKey('user_hsms', foreignKey);
      }
    }

    if (hsmKeysTable) {
      const hsmKeysForeignKeys = hsmKeysTable.foreignKeys;
      for (const foreignKey of hsmKeysForeignKeys) {
        await queryRunner.dropForeignKey('hsm_keys', foreignKey);
      }
    }

    // Drop indexes
    await queryRunner.dropIndex('hsm_keys', 'IDX_HSM_KEY_HSM');
    await queryRunner.dropIndex('hsm_keys', 'IDX_HSM_KEY_USER');
    await queryRunner.dropIndex('hsm_keys', 'IDX_HSM_KEY_STATUS');
    await queryRunner.dropIndex('hsm_keys', 'IDX_HSM_KEY_TYPE');
    await queryRunner.dropIndex('hsm_keys', 'IDX_HSM_KEY_NAME');
    await queryRunner.dropIndex('hsms', 'IDX_HSM_ACTIVE');
    await queryRunner.dropIndex('hsms', 'IDX_HSM_NAME');

    // Drop tables
    await queryRunner.dropTable('user_hsms');
    await queryRunner.dropTable('hsm_keys');
    await queryRunner.dropTable('hsms');

    // Drop enums
    await queryRunner.query(`DROP TYPE IF EXISTS "hsm_key_status_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "ecc_curve_type_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "hsm_key_type_enum"`);
  }
}
