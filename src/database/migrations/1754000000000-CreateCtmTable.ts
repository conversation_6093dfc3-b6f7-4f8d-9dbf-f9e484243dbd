import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateCtmTable1754000000000 implements MigrationInterface {
  name = 'CreateCtmTable1754000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create CTMs table
    await queryRunner.createTable(
      new Table({
        name: 'ctms',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'ipAddress',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'username',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'password',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'domain',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'seqrngIpAddress',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'seqrngApiToken',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create index on name for faster lookups
    await queryRunner.createIndex(
      'ctms',
      new TableIndex({
        name: 'IDX_CTM_NAME',
        columnNames: ['name'],
      }),
    );

    // Create index on isActive for filtering
    await queryRunner.createIndex(
      'ctms',
      new TableIndex({
        name: 'IDX_CTM_ACTIVE',
        columnNames: ['isActive'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.dropIndex('ctms', 'IDX_CTM_ACTIVE');
    await queryRunner.dropIndex('ctms', 'IDX_CTM_NAME');
    
    // Drop table
    await queryRunner.dropTable('ctms');
  }
}
