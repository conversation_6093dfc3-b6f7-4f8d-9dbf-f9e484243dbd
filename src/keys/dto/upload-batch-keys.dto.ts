import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsBoolean, IsEnum, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { KeyAlgorithm } from './upload-key.dto';

export class UploadBatchKeysDto {
  @ApiProperty({
    example: 'batch-key',
    description: 'Prefijo para los nombres de las llaves',
  })
  @IsString()
  key_name_prefix: string;

  @ApiProperty({
    example: KeyAlgorithm.AES,
    description: 'Algoritmo de las llaves',
    enum: KeyAlgorithm,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => value ? value.toLowerCase() : value)
  @IsEnum(KeyAlgorithm)
  algorithm?: KeyAlgorithm = KeyAlgorithm.AES;

  @ApiProperty({
    example: 32,
    description: 'Número de bytes para cada llave',
    minimum: 1,
    maximum: 1024,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1024)
  num_bytes?: number = 32;

  // owner se determina automáticamente basado en el usuario autenticado

  @ApiProperty({
    example: false,
    description: 'Si las llaves son exportables',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  exportable?: boolean = false;

  @ApiProperty({
    example: 5,
    description: 'Número de llaves a generar',
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  key_count?: number = 1;

  @ApiProperty({
    example: 1,
    description: 'Número inicial para la numeración de llaves',
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  key_num_start?: number = 1;

  @ApiProperty({
    example: 'uuid-string',
    description: 'ID del CTM a utilizar',
  })
  @IsUUID()
  ctmId: string;
}
