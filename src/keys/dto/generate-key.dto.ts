import { <PERSON><PERSON><PERSON><PERSON>, IsInt, Min, <PERSON>, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GenerateKeyDto {
  @ApiProperty({
    example: 32,
    description: 'Número de bytes para la llave',
    minimum: 1,
    maximum: 1024,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1024)
  num_bytes?: number = 32;

  @ApiProperty({
    example: 'uuid-string',
    description: 'ID del CTM a utilizar',
  })
  @IsUUID()
  ctmId: string;
}
