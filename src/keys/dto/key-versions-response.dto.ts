import { ApiProperty } from '@nestjs/swagger';
import { KeyResponseDto } from './key-response.dto';

export class KeyVersionDto extends KeyResponseDto {
  @ApiProperty({
    description: 'Base key ID (null for base keys)',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    nullable: true,
  })
  baseKeyId: string | null;

  @ApiProperty({
    description: 'Version number (0 for base keys, 1+ for versions)',
    example: 1,
  })
  declare version: number;
}

export class KeyVersionsResponseDto {
  @ApiProperty({
    description: 'Base key ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  baseKeyId: string;

  @ApiProperty({
    description: 'Total number of versions including base key',
    example: 3,
  })
  totalVersions: number;

  @ApiProperty({
    description: 'List of all versions ordered by version number',
    type: [KeyVersionDto],
  })
  versions: KeyVersionDto[];
}
