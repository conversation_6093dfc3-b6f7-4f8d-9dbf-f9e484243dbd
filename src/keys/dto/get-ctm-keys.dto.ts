import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, Min, Max, IsEnum, IsBoolean, IsUUID } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { KeyType, KeyAlgorithm, KeyStatus } from '../entities/key.entity';

export class GetCtmKeysDto {
  @ApiProperty({
    example: 'uuid-string',
    description: 'ID del CTM del cual obtener las llaves',
  })
  @IsUUID()
  ctmId: string;

  @ApiPropertyOptional({
    example: 1,
    description: 'Número de página',
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    example: 10,
    description: 'Número de elementos por página',
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    example: 'encryption',
    description: 'Buscar por nombre de llave o nombre CTM',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    enum: KeyType,
    description: 'Filtrar por tipo de llave',
  })
  @IsOptional()
  @IsEnum(KeyType)
  type?: KeyType;

  @ApiPropertyOptional({
    enum: KeyAlgorithm,
    description: 'Filtrar por algoritmo',
  })
  @IsOptional()
  @IsEnum(KeyAlgorithm)
  algorithm?: KeyAlgorithm;

  @ApiPropertyOptional({
    enum: KeyStatus,
    description: 'Filtrar por estado',
  })
  @IsOptional()
  @IsEnum(KeyStatus)
  status?: KeyStatus;

  @ApiPropertyOptional({
    example: true,
    description: 'Filtrar por llaves subidas a CTM',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  uploadedToCtm?: boolean;

  @ApiPropertyOptional({
    example: 'createdAt',
    description: 'Campo por el cual ordenar',
    default: 'createdAt',
    enum: ['createdAt', 'updatedAt', 'name', 'type', 'status'],
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    example: 'DESC',
    description: 'Orden de clasificación',
    default: 'DESC',
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
