import { IsString, IsOptional, IsBoolean, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateKeyVersionDto {
  @ApiProperty({
    example: 'uuid-string',
    description: 'ID del CTM a utilizar',
  })
  @IsUUID()
  ctmId: string;

  @ApiPropertyOptional({
    description: 'Whether to archive the previous version after creating the new one',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  archivePrevious?: boolean;
}

export class CreateKeyVersionResponseDto {
  @ApiProperty({
    description: 'ID of the key',
    example: 'key-12345-abcde',
  })
  keyId: string;

  @ApiProperty({
    description: 'Name of the key',
    example: 'my-encryption-key',
  })
  keyName: string;

  @ApiProperty({
    description: 'Algorithm of the key',
    example: 'aes',
  })
  algorithm: string;

  @ApiProperty({
    description: 'ID of the new version created',
    example: 'version-67890-fghij',
  })
  versionId: string;

  @ApiProperty({
    description: 'Version number',
    example: 2,
  })
  versionNumber: number;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Length of the quantum material used',
    example: 32,
  })
  materialLength: number;

  @ApiProperty({
    description: 'Whether previous version was archived',
    example: false,
  })
  archivePrevious: boolean;

  @ApiProperty({
    description: 'Whether archiving was successful (if requested)',
    example: true,
  })
  archiveSuccess: boolean;

  @ApiProperty({
    description: 'Entropy report from quantum generation',
    type: 'object',
    additionalProperties: true,
  })
  entropyReport: any;
}
