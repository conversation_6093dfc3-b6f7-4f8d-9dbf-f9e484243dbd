import { Injectable, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UsersService } from '../users/users.service';
import { CtmService } from '../ctm/ctm.service';
import { SeqrngClientService, CtmConfig, SeqrngConfig } from '../common/services/seqrng-client.service';
import { Key, KeyType, KeyStatus, KeyAlgorithm } from './entities/key.entity';
import { GenerateBytesDto } from './dto/generate-bytes.dto';
import { GenerateKeyDto } from './dto/generate-key.dto';
import { UploadKeyDto } from './dto/upload-key.dto';
import { UploadBatchKeysDto } from './dto/upload-batch-keys.dto';
import { GetKeysDto } from './dto/get-keys.dto';
import { KeyResponseDto, KeyListResponseDto } from './dto/key-response.dto';

@Injectable()
export class KeysService {
  private readonly logger = new Logger(KeysService.name);

  constructor(
    @InjectRepository(Key)
    private readonly keyRepository: Repository<Key>,
    private readonly usersService: UsersService,
    private readonly ctmService: CtmService,
    private readonly seqrngClientService: SeqrngClientService,
  ) {}

  /**
   * Map algorithm string to KeyAlgorithm enum
   */
  private mapAlgorithmToEnum(algorithm: string): KeyAlgorithm {
    const algorithmLower = algorithm.toLowerCase();

    switch (algorithmLower) {
      case 'aes':
        return KeyAlgorithm.AES;
      case 'aria':
        return KeyAlgorithm.ARIA;
      case 'rsa':
        return KeyAlgorithm.RSA;
      case 'hmac-sha1':
      case 'hmac_sha1':
        return KeyAlgorithm.HMAC_SHA1;
      case 'hmac-sha256':
      case 'hmac_sha256':
        return KeyAlgorithm.HMAC_SHA256;
      case 'hmac-sha384':
      case 'hmac_sha384':
        return KeyAlgorithm.HMAC_SHA384;
      case 'hmac-sha512':
      case 'hmac_sha512':
        return KeyAlgorithm.HMAC_SHA512;
      default:
        // Default to AES if unknown algorithm
        return KeyAlgorithm.AES;
    }
  }

  /**
   * Generate random bytes using specified CTM and SeqRNG configuration
   */
  async generateRandomBytes(userId: string, ctmId: string, generateBytesDto: GenerateBytesDto) {
    this.logger.log(`Generating random bytes for user ${userId} using CTM ${ctmId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: `random-bytes-${Date.now()}`,
      type: KeyType.RANDOM_BYTES,
      algorithm: null,
      numBytes: generateBytesDto.num_bytes,
      status: KeyStatus.GENERATED,
      owner: null,
      exportable: false,
      uploadedToCtm: false,
      ctmKeyName: null,
      userId,
      ctmId,
    });

    try {
      const result = await this.seqrngClientService.generateRandomBytes(
        generateBytesDto,
        ctmConfig,
        seqrngConfig,
      );

      // Update key record with success
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully generated ${generateBytesDto.num_bytes} random bytes for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to generate random bytes for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate random bytes: ${error.message}`);
    }
  }

  /**
   * Generate hexadecimal key using specified CTM and SeqRNG configuration
   */
  async generateHexKey(userId: string, ctmId: string, generateKeyDto: GenerateKeyDto) {
    this.logger.log(`Generating hex key for user ${userId} using CTM ${ctmId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: `hex-key-${Date.now()}`,
      type: KeyType.HEX_KEY,
      algorithm: null, // Will be set based on the generated key
      numBytes: generateKeyDto.num_bytes,
      status: KeyStatus.GENERATED,
      owner: null, // No owner specified for generated keys
      exportable: false,
      uploadedToCtm: false,
      ctmKeyName: null,
      active: true, // New keys are active by default
      userId,
      ctmId,
    });

    try {
      const result = await this.seqrngClientService.generateHexKey(
        generateKeyDto,
        ctmConfig,
        seqrngConfig,
      );

      // Update key record with success
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully generated hex key for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to generate hex key for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate hex key: ${error.message}`);
    }
  }

  /**
   * Generate alphanumeric key using specified CTM and SeqRNG configuration
   */
  async generateAlphanumericKey(userId: string, ctmId: string, generateKeyDto: GenerateKeyDto) {
    this.logger.log(`Generating alphanumeric key for user ${userId} using CTM ${ctmId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: `alphanumeric-key-${Date.now()}`,
      type: KeyType.ALPHANUMERIC_KEY,
      algorithm: null, // Will be set based on the generated key
      numBytes: generateKeyDto.num_bytes,
      status: KeyStatus.GENERATED,
      owner: null, // No owner specified for generated keys
      exportable: false,
      uploadedToCtm: false,
      ctmKeyName: null,
      active: true, // New keys are active by default
      userId,
      ctmId,
    });

    try {
      const result = await this.seqrngClientService.generateAlphanumericKey(
        generateKeyDto,
        ctmConfig,
        seqrngConfig,
      );

      // Update key record with success
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully generated alphanumeric key for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to generate alphanumeric key for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate alphanumeric key: ${error.message}`);
    }
  }

  /**
   * Upload key to CTM using specified CTM configuration
   */
  async uploadKeyToCtm(userId: string, ctmId: string, uploadKeyDto: UploadKeyDto) {
    this.logger.log(`Uploading key '${uploadKeyDto.key_name}' to CTM ${ctmId} for user ${userId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    // Get user information to determine owner
    const user = await this.usersService.findOne(userId);
    const owner = this.determineOwner(user);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: uploadKeyDto.key_name,
      type: KeyType.HEX_KEY, // Assuming uploaded keys are hex by default
      algorithm: uploadKeyDto.algorithm ? this.mapAlgorithmToEnum(uploadKeyDto.algorithm) : null,
      numBytes: uploadKeyDto.num_bytes || 32,
      status: KeyStatus.GENERATED,
      owner: owner,
      exportable: uploadKeyDto.exportable || false,
      uploadedToCtm: false,
      ctmKeyName: uploadKeyDto.key_name,
      active: true, // New keys are active by default
      userId,
      ctmId,
    });

    try {
      // Add owner to the DTO for the CTM request
      const uploadKeyDtoWithOwner = {
        ...uploadKeyDto,
        owner: owner,
      };

      const result = await this.seqrngClientService.uploadKeyToCtm(
        uploadKeyDtoWithOwner,
        ctmConfig,
        seqrngConfig,
      );

      // Update key record with success
      keyRecord.status = KeyStatus.UPLOADED_TO_CTM;
      keyRecord.uploadedToCtm = true;
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      keyRecord.ctmKeyId = result.data?.ctm_key_id || null;
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully uploaded key '${uploadKeyDto.key_name}' to CTM for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to upload key to CTM for user ${userId}`, error);
      throw new BadRequestException(`Failed to upload key to CTM: ${error.message}`);
    }
  }

  /**
   * Upload multiple keys to CTM using specified CTM configuration
   */
  async uploadBatchKeysToCtm(userId: string, ctmId: string, uploadBatchKeysDto: UploadBatchKeysDto) {
    this.logger.log(`Uploading batch keys with prefix '${uploadBatchKeysDto.key_name_prefix}' to CTM ${ctmId} for user ${userId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    // Get user information to determine owner
    const user = await this.usersService.findOne(userId);
    const owner = this.determineOwner(user);

    try {
      // Add owner to the DTO for the CTM request
      const uploadBatchKeysDtoWithOwner = {
        ...uploadBatchKeysDto,
        owner: owner,
      };

      const result = await this.seqrngClientService.uploadBatchKeysToCtm(
        uploadBatchKeysDtoWithOwner,
        ctmConfig,
        seqrngConfig,
      );

      this.logger.log(`Successfully uploaded batch keys to CTM for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to upload batch keys to CTM for user ${userId}`, error);
      throw new BadRequestException(`Failed to upload batch keys to CTM: ${error.message}`);
    }
  }

  /**
   * Check if key exists in CTM using specified CTM configuration
   */
  async checkKeyExists(userId: string, ctmId: string, keyName: string) {
    this.logger.log(`Checking if key '${keyName}' exists in CTM ${ctmId} for user ${userId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    try {
      const result = await this.seqrngClientService.checkKeyExists(keyName, ctmConfig, seqrngConfig);

      this.logger.log(`Successfully checked key existence for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to check key existence for user ${userId}`, error);
      throw new BadRequestException(`Failed to check key existence: ${error.message}`);
    }
  }

  /**
   * Get CTM authentication token using specified CTM configuration
   */
  async getCtmToken(userId: string, ctmId: string) {
    this.logger.log(`Getting CTM token for user ${userId} using CTM ${ctmId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    try {
      const result = await this.seqrngClientService.getCtmToken(ctmConfig, seqrngConfig);

      this.logger.log(`Successfully obtained CTM token for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to get CTM token for user ${userId}`, error);
      throw new BadRequestException(`Failed to get CTM token: ${error.message}`);
    }
  }

  /**
   * Determine owner based on user information
   */
  private determineOwner(user: any): string {
    // If user has a company, use it as owner
    if (user.company && user.company.trim()) {
      return user.company.trim().toLowerCase().replace(/\s+/g, '_');
    }

    // Otherwise, use firstName_lastName format
    return `${user.firstName}_${user.lastName}`.toLowerCase();
  }



  /**
   * Get user's keys with pagination and filters
   * Returns only the latest version of each key when multiple versions exist
   * @deprecated Use getCtmKeys instead
   */
  async getUserKeys(userId: string, getKeysDto: GetKeysDto): Promise<KeyListResponseDto> {
    this.logger.log(`Getting keys for user ${userId}`);

    const {
      page = 1,
      limit = 10,
      type,
      algorithm,
      status,
      uploadedToCtm,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = getKeysDto;

    // First, get all keys for the user with filters (without pagination)
    const baseQueryBuilder = this.keyRepository
      .createQueryBuilder('key')
      .where('key.userId = :userId', { userId });

    // Apply filters
    if (type) {
      baseQueryBuilder.andWhere('key.type = :type', { type });
    }

    if (algorithm) {
      baseQueryBuilder.andWhere('key.algorithm = :algorithm', { algorithm });
    }

    if (status) {
      baseQueryBuilder.andWhere('key.status = :status', { status });
    }

    if (uploadedToCtm !== undefined) {
      baseQueryBuilder.andWhere('key.uploadedToCtm = :uploadedToCtm', { uploadedToCtm });
    }

    if (search) {
      baseQueryBuilder.andWhere(
        '(key.name ILIKE :search OR key.ctmKeyName ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Get all keys that match the filters
    const allKeys = await baseQueryBuilder.getMany();

    // Group keys by base key (either their own ID if they're base keys, or their baseKeyId)
    const keyGroups = new Map<string, Key[]>();

    for (const key of allKeys) {
      const groupKey = key.baseKeyId || key.id; // Use baseKeyId if it exists, otherwise use the key's own ID
      if (!keyGroups.has(groupKey)) {
        keyGroups.set(groupKey, []);
      }
      keyGroups.get(groupKey)!.push(key);
    }

    // Get the latest version from each group
    const latestKeys: Key[] = [];
    for (const [, keys] of keyGroups) {
      // Sort by version descending and take the first one (highest version)
      const sortedKeys = keys.sort((a, b) => b.version - a.version);
      latestKeys.push(sortedKeys[0]);
    }

    // Apply sorting to the latest keys
    const validSortFields = ['createdAt', 'updatedAt', 'name', 'type', 'status'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';

    latestKeys.sort((a, b) => {
      const aValue = a[sortField as keyof Key];
      const bValue = b[sortField as keyof Key];

      // Handle null values
      if (aValue === null && bValue === null) return 0;
      if (aValue === null) return sortOrder === 'ASC' ? 1 : -1;
      if (bValue === null) return sortOrder === 'ASC' ? -1 : 1;

      if (sortOrder === 'ASC') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      }
    });

    // Apply pagination to the latest keys
    const total = latestKeys.length;
    const offset = (page - 1) * limit;
    const paginatedKeys = latestKeys.slice(offset, offset + limit);

    // Calculate statistics based on all latest keys (not just paginated ones)
    const successful = latestKeys.filter(key => key.status !== KeyStatus.FAILED).length;
    const failed = latestKeys.length - successful;
    const uploadedToCtmCount = latestKeys.filter(key => key.uploadedToCtm).length;

    // Transform to DTOs
    const keyDtos: KeyResponseDto[] = paginatedKeys.map(key => ({
      id: key.id,
      name: key.name,
      type: key.type,
      algorithm: key.algorithm,
      numBytes: key.numBytes,
      status: key.status,
      owner: key.owner,
      exportable: key.exportable,
      uploadedToCtm: key.uploadedToCtm,
      ctmKeyName: key.ctmKeyName,
      ctmKeyId: key.ctmKeyId,
      entropyReport: key.entropyReport,
      errorMessage: key.errorMessage,
      createdAt: key.createdAt,
      updatedAt: key.updatedAt,
      isSuccessful: key.isSuccessful,
      displayName: key.displayName,
      version: key.version,
      active: key.active,
    }));

    return {
      keys: keyDtos,
      total,
      successful,
      failed,
      uploadedToCtm: uploadedToCtmCount,
    };
  }

  /**
   * Get CTM's keys with pagination and filters
   * Returns only the latest version of each key when multiple versions exist
   */
  async getCtmKeys(userId: string, ctmId: string, getKeysDto: GetKeysDto): Promise<KeyListResponseDto> {
    this.logger.log(`Getting keys for CTM ${ctmId} for user ${userId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const {
      page = 1,
      limit = 10,
      type,
      algorithm,
      status,
      uploadedToCtm,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = getKeysDto;

    // First, get all keys for the CTM with filters (without pagination)
    const baseQueryBuilder = this.keyRepository
      .createQueryBuilder('key')
      .leftJoinAndSelect('key.user', 'user')
      .where('key.ctmId = :ctmId', { ctmId });

    // Apply filters
    if (type) {
      baseQueryBuilder.andWhere('key.type = :type', { type });
    }

    if (algorithm) {
      baseQueryBuilder.andWhere('key.algorithm = :algorithm', { algorithm });
    }

    if (status) {
      baseQueryBuilder.andWhere('key.status = :status', { status });
    }

    if (uploadedToCtm !== undefined) {
      baseQueryBuilder.andWhere('key.uploadedToCtm = :uploadedToCtm', { uploadedToCtm });
    }

    if (search) {
      baseQueryBuilder.andWhere(
        '(key.name ILIKE :search OR key.ctmKeyName ILIKE :search OR key.owner ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Get all keys that match the filters
    const allKeys = await baseQueryBuilder.getMany();

    // Group keys by base key (either their own ID if they're base keys, or their baseKeyId)
    const keyGroups = new Map<string, Key[]>();

    for (const key of allKeys) {
      const groupKey = key.baseKeyId || key.id; // Use baseKeyId if it exists, otherwise use the key's own ID
      if (!keyGroups.has(groupKey)) {
        keyGroups.set(groupKey, []);
      }
      keyGroups.get(groupKey)!.push(key);
    }

    // Get the latest version from each group
    const latestKeys: Key[] = [];
    for (const [, keys] of keyGroups) {
      // Sort by version descending and take the first one (highest version)
      const sortedKeys = keys.sort((a, b) => b.version - a.version);
      latestKeys.push(sortedKeys[0]);
    }

    // Apply sorting to the latest keys
    const validSortFields = ['createdAt', 'updatedAt', 'name', 'type', 'status'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';

    latestKeys.sort((a, b) => {
      const aValue = a[sortField as keyof Key];
      const bValue = b[sortField as keyof Key];

      // Handle null values
      if (aValue === null && bValue === null) return 0;
      if (aValue === null) return sortOrder === 'ASC' ? 1 : -1;
      if (bValue === null) return sortOrder === 'ASC' ? -1 : 1;

      if (sortOrder === 'ASC') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      }
    });

    // Apply pagination to the latest keys
    const total = latestKeys.length;
    const offset = (page - 1) * limit;
    const paginatedKeys = latestKeys.slice(offset, offset + limit);

    // Calculate statistics based on all latest keys (not just paginated ones)
    const successful = latestKeys.filter(key => key.status !== KeyStatus.FAILED).length;
    const failed = latestKeys.length - successful;
    const uploadedToCtmCount = latestKeys.filter(key => key.uploadedToCtm).length;

    // Transform to DTOs with user information
    const keyDtos: KeyResponseDto[] = paginatedKeys.map(key => ({
      id: key.id,
      name: key.name,
      type: key.type,
      algorithm: key.algorithm,
      numBytes: key.numBytes,
      status: key.status,
      owner: key.owner,
      exportable: key.exportable,
      uploadedToCtm: key.uploadedToCtm,
      ctmKeyName: key.ctmKeyName,
      ctmKeyId: key.ctmKeyId,
      entropyReport: key.entropyReport,
      errorMessage: key.errorMessage,
      createdAt: key.createdAt,
      updatedAt: key.updatedAt,
      isSuccessful: key.isSuccessful,
      displayName: key.displayName,
      version: key.version,
      active: key.active,
      // Add user information for CTM view
      createdBy: key.user ? `${key.user.firstName} ${key.user.lastName}` : 'Unknown',
    }));

    return {
      keys: keyDtos,
      total,
      successful,
      failed,
      uploadedToCtm: uploadedToCtmCount,
    };
  }

  /**
   * Get a specific key by ID for a user
   */
  async getUserKey(userId: string, keyId: string): Promise<KeyResponseDto> {
    this.logger.log(`Getting key ${keyId} for user ${userId}`);

    const key = await this.keyRepository.findOne({
      where: { id: keyId, userId },
    });

    if (!key) {
      throw new BadRequestException('Key not found');
    }

    return {
      id: key.id,
      name: key.name,
      type: key.type,
      algorithm: key.algorithm,
      numBytes: key.numBytes,
      status: key.status,
      owner: key.owner,
      exportable: key.exportable,
      uploadedToCtm: key.uploadedToCtm,
      ctmKeyName: key.ctmKeyName,
      ctmKeyId: key.ctmKeyId,
      entropyReport: key.entropyReport,
      errorMessage: key.errorMessage,
      createdAt: key.createdAt,
      updatedAt: key.updatedAt,
      isSuccessful: key.isSuccessful,
      displayName: key.displayName,
      version: key.version,
      active: key.active,
    };
  }

  /**
   * Delete a key by ID for a user
   */
  async deleteUserKey(userId: string, keyId: string): Promise<void> {
    this.logger.log(`Deleting key ${keyId} for user ${userId}`);

    const result = await this.keyRepository.delete({
      id: keyId,
      userId,
    });

    if (result.affected === 0) {
      throw new BadRequestException('Key not found');
    }

    this.logger.log(`Successfully deleted key ${keyId} for user ${userId}`);
  }

  /**
   * Create a new version of an existing key with quantum material
   */
  async createKeyVersion(userId: string, ctmId: string, keyId: string, archivePrevious: boolean = false) {
    this.logger.log(`Creating new version for key ${keyId} for user ${userId} using CTM ${ctmId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    // Get user information to determine owner
    const user = await this.usersService.findOne(userId);
    const owner = this.determineOwner(user);

    try {
      // First, find the base key in our database to get version info
      const baseKey = await this.keyRepository.findOne({
        where: { ctmKeyId: keyId, userId },
      });

      if (!baseKey) {
        throw new BadRequestException(`Key with CTM ID '${keyId}' not found in database`);
      }

      // Determine the base key ID and next version number
      const baseKeyId = baseKey.baseKeyId || baseKey.id; // If it's already a version, use its baseKeyId
      const currentMaxVersion = await this.keyRepository
        .createQueryBuilder('key')
        .select('MAX(key.version)', 'maxVersion')
        .where('(key.baseKeyId = :baseKeyId OR key.id = :baseKeyId)', { baseKeyId })
        .getRawOne();

      const nextVersion = (currentMaxVersion?.maxVersion || 0) + 1;

      // Get key information from CTM to validate it exists and get details
      const keyInfoResult = await this.seqrngClientService.getKeyInfo(
        keyId,
        ctmConfig,
        seqrngConfig,
      );

      if (keyInfoResult.status !== 'success') {
        throw new BadRequestException(`Key with ID '${keyId}' not found or inaccessible in CTM`);
      }

      const keyInfo = keyInfoResult.data;
      const keyName = keyInfo.name || 'unknown';
      const algorithm = keyInfo.algorithm || 'aes';

      // Create new version with quantum material
      const result = await this.seqrngClientService.createKeyVersion(
        keyId,
        archivePrevious,
        ctmConfig,
        seqrngConfig,
      );

      if (result.status !== 'success') {
        throw new BadRequestException(`Failed to create new version: ${result.message}`);
      }

      // If archivePrevious is true, find and deactivate the current active version
      let archiveSuccess = true;
      let archiveDate: Date | null = null;

      if (archivePrevious) {
        try {
          // Find the current active version (highest version number that is active)
          const currentActiveVersion = await this.keyRepository
            .createQueryBuilder('key')
            .where('(key.baseKeyId = :baseKeyId OR key.id = :baseKeyId)', { baseKeyId })
            .andWhere('key.active = :active', { active: true })
            .orderBy('key.version', 'DESC')
            .getOne();

          if (currentActiveVersion) {
            // Extract archive information from the seqrng-api response
            const archiveInfo = result.data.archive_info;
            archiveSuccess = result.data.archive_success;

            if (archiveSuccess && archiveInfo?.archive_date) {
              // Parse the archive date from CTM response
              archiveDate = new Date(archiveInfo.archive_date);
            }

            // Mark the current active version as inactive and set archive date
            const updateData: any = { active: false };
            if (archiveDate) {
              updateData.archiveDate = archiveDate;
            }

            await this.keyRepository.update(
              { id: currentActiveVersion.id },
              updateData
            );

            this.logger.log(`Archived previous version ${currentActiveVersion.id} (version ${currentActiveVersion.version})`);
            if (archiveDate) {
              this.logger.log(`Archive date set to: ${archiveDate.toISOString()}`);
            }
          }
        } catch (archiveError) {
          this.logger.error(`Failed to archive previous version: ${archiveError.message}`);
          archiveSuccess = false;
          // Continue with version creation even if archiving fails
        }
      }

      // Create a new key record for the version
      const keyRecord = this.keyRepository.create({
        name: keyName,
        type: KeyType.HEX_KEY, // Assuming versioned keys are hex by default
        algorithm: this.mapAlgorithmToEnum(algorithm),
        numBytes: result.data.material_length || 32,
        status: KeyStatus.UPLOADED_TO_CTM,
        owner: owner,
        exportable: false, // Versioned keys are typically not exportable
        uploadedToCtm: true,
        ctmKeyName: keyName,
        ctmKeyId: result.data.version_id, // Store the version ID as the CTM key ID
        entropyReport: JSON.stringify(result.data.entropy_report || {}),
        baseKeyId: baseKeyId, // Reference to the base key
        version: nextVersion, // Set the version number
        active: true, // New version is always active
        userId,
        ctmId,
      });

      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully created new version for key ${keyId} for user ${userId}`);

      // Return enhanced result with archive information
      return {
        ...result,
        data: {
          ...result.data,
          archivePrevious,
          archiveSuccess,
          archiveDate: archiveDate?.toISOString() || null,
        }
      };
    } catch (error) {
      this.logger.error(`Failed to create key version for user ${userId}`, error);
      throw new BadRequestException(`Failed to create key version: ${error.message}`);
    }
  }

  /**
   * Get information about a specific key from CTM
   */
  async getKeyInfo(userId: string, ctmId: string, keyId: string) {
    this.logger.log(`Getting key info for ${keyId} for user ${userId} using CTM ${ctmId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    try {
      const result = await this.seqrngClientService.getKeyInfo(
        keyId,
        ctmConfig,
        seqrngConfig,
      );

      this.logger.log(`Successfully retrieved key info for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to get key info for user ${userId}`, error);
      throw new BadRequestException(`Failed to get key information: ${error.message}`);
    }
  }

  /**
   * Get all versions of a key by key ID
   * If the provided key has a baseKeyId, uses that to get all versions
   * Otherwise, treats the provided ID as the base key ID
   */
  async getKeyVersions(userId: string, keyId: string) {
    this.logger.log(`Getting all versions for key ${keyId} for user ${userId}`);

    try {
      // First, find the key by the provided ID
      const key = await this.keyRepository.findOne({
        where: { id: keyId, userId },
      });

      if (!key) {
        throw new BadRequestException(`Key with ID '${keyId}' not found`);
      }

      // Determine the actual base key ID
      // If the key has a baseKeyId, use that; otherwise, use the key's own ID
      const actualBaseKeyId = key.baseKeyId || key.id;

      this.logger.log(`Using base key ID ${actualBaseKeyId} for versions lookup`);

      // Find all versions of the key (including the base key itself)
      const versions = await this.keyRepository.find({
        where: [
          { id: actualBaseKeyId, userId }, // The base key itself
          { baseKeyId: actualBaseKeyId, userId }, // All versions of the base key
        ],
        order: { version: 'ASC' },
      });

      if (versions.length === 0) {
        throw new BadRequestException(`No versions found for base key '${actualBaseKeyId}'`);
      }

      // Transform to DTOs
      const versionDtos = versions.map(versionKey => ({
        id: versionKey.id,
        name: versionKey.name,
        type: versionKey.type,
        algorithm: versionKey.algorithm,
        numBytes: versionKey.numBytes,
        status: versionKey.status,
        owner: versionKey.owner,
        exportable: versionKey.exportable,
        uploadedToCtm: versionKey.uploadedToCtm,
        ctmKeyName: versionKey.ctmKeyName,
        ctmKeyId: versionKey.ctmKeyId,
        baseKeyId: versionKey.baseKeyId,
        version: versionKey.version,
        active: versionKey.active,
        createdAt: versionKey.createdAt,
        updatedAt: versionKey.updatedAt,
        isSuccessful: versionKey.isSuccessful,
        displayName: versionKey.displayName,
        entropyReport: versionKey.entropyReport,
        errorMessage: versionKey.errorMessage,
      }));

      this.logger.log(`Found ${versions.length} versions for base key ${actualBaseKeyId}`);
      return {
        baseKeyId: actualBaseKeyId,
        totalVersions: versions.length,
        versions: versionDtos,
      };
    } catch (error) {
      this.logger.error(`Failed to get key versions for user ${userId}`, error);
      throw new BadRequestException(`Failed to get key versions: ${error.message}`);
    }
  }

  /**
   * Synchronize CTM keys with local database
   * Fetches all keys from specified CTM environment and saves new ones to database
   */
  async synchronizeCtmKeys(userId: string, ctmId: string) {
    this.logger.log(`Starting CTM synchronization for user ${userId} using CTM ${ctmId}`);

    // Validate user has access to this CTM
    await this.validateUserCtmAccess(userId, ctmId);

    const ctmConfig = await this.getCtmConfig(ctmId);
    const seqrngConfig = await this.getCtmSeqrngConfig(ctmId);

    // Get user information to determine owner
    const user = await this.usersService.findOne(userId);
    const owner = this.determineOwner(user);

    try {
      // Get all keys from CTM
      const result = await this.seqrngClientService.listCtmKeys(ctmConfig, seqrngConfig);

      if (result.status !== 'success') {
        throw new BadRequestException(`Failed to retrieve keys from CTM: ${result.message}`);
      }

      const ctmKeys = result.data?.keys || [];
      const totalCtmKeys = result.data?.total || 0;

      this.logger.log(`Found ${totalCtmKeys} keys in CTM environment`);

      // Get existing keys in database for this user
      const existingKeys = await this.keyRepository.find({
        where: { userId },
        select: ['ctmKeyId'],
      });

      const existingCtmKeyIds = new Set(
        existingKeys
          .map(key => key.ctmKeyId)
          .filter(id => id !== null)
      );

      this.logger.log(`Found ${existingCtmKeyIds.size} existing keys in database`);

      // Filter out keys that already exist in database
      const newKeys = ctmKeys.filter(ctmKey => !existingCtmKeyIds.has(ctmKey.id));

      this.logger.log(`Found ${newKeys.length} new keys to synchronize`);

      const synchronizedKeys: Array<{
        id: string;
        ctmKeyId: string;
        name: string;
        algorithm: string;
        status: string;
      }> = [];

      const failedKeys: Array<{
        ctmKeyId: string;
        name: string;
        error: string;
      }> = [];

      // Process each new key
      for (const ctmKey of newKeys) {
        try {
          // Map CTM algorithm to our enum
          const algorithm = this.mapCtmAlgorithmToEnum(ctmKey.algorithm);

          // Determine key type based on algorithm
          const keyType = this.determineKeyType(ctmKey.algorithm);

          // Create key record
          const keyRecord = this.keyRepository.create({
            name: ctmKey.name || 'imported-key',
            type: keyType,
            algorithm: algorithm,
            numBytes: ctmKey.size || 32,
            status: KeyStatus.UPLOADED_TO_CTM,
            owner: owner,
            exportable: !ctmKey.unexportable,
            uploadedToCtm: true,
            ctmKeyName: ctmKey.name,
            ctmKeyId: ctmKey.id,
            entropyReport: null, // CTM keys don't have entropy reports
            active: ctmKey.state === 'Active',
            userId,
            ctmId,
          });

          await this.keyRepository.save(keyRecord);
          synchronizedKeys.push({
            id: keyRecord.id,
            ctmKeyId: ctmKey.id,
            name: ctmKey.name,
            algorithm: ctmKey.algorithm,
            status: 'synchronized'
          });

          this.logger.log(`Synchronized key: ${ctmKey.name} (${ctmKey.id})`);
        } catch (error) {
          this.logger.error(`Failed to synchronize key ${ctmKey.id}:`, error);
          failedKeys.push({
            ctmKeyId: ctmKey.id,
            name: ctmKey.name,
            error: error.message
          });
        }
      }

      const summary = {
        totalCtmKeys,
        existingInDatabase: existingCtmKeyIds.size,
        newKeysSynchronized: synchronizedKeys.length,
        failedSynchronizations: failedKeys.length,
        synchronizedKeys,
        failedKeys
      };

      this.logger.log(`CTM synchronization completed for user ${userId}. Synchronized: ${synchronizedKeys.length}, Failed: ${failedKeys.length}`);

      return {
        status: 'success',
        message: `Synchronized ${synchronizedKeys.length} new keys from CTM`,
        data: summary
      };

    } catch (error) {
      this.logger.error(`Failed to synchronize CTM keys for user ${userId}`, error);
      throw new BadRequestException(`Failed to synchronize CTM keys: ${error.message}`);
    }
  }

  /**
   * Map CTM algorithm to our KeyAlgorithm enum
   */
  private mapCtmAlgorithmToEnum(ctmAlgorithm: string): KeyAlgorithm {
    if (!ctmAlgorithm) return KeyAlgorithm.AES;

    const algorithmLower = ctmAlgorithm.toLowerCase();

    switch (algorithmLower) {
      case 'aes':
        return KeyAlgorithm.AES;
      case 'aria':
        return KeyAlgorithm.ARIA;
      case 'rsa':
        return KeyAlgorithm.RSA;
      case 'hmac-sha1':
      case 'hmac_sha1':
        return KeyAlgorithm.HMAC_SHA1;
      case 'hmac-sha256':
      case 'hmac_sha256':
        return KeyAlgorithm.HMAC_SHA256;
      case 'hmac-sha384':
      case 'hmac_sha384':
        return KeyAlgorithm.HMAC_SHA384;
      case 'hmac-sha512':
      case 'hmac_sha512':
        return KeyAlgorithm.HMAC_SHA512;
      default:
        return KeyAlgorithm.AES;
    }
  }

  /**
   * Validate that user has access to the specified CTM
   */
  private async validateUserCtmAccess(userId: string, ctmId: string): Promise<void> {
    const userCtms = await this.ctmService.getUserCtms(userId);
    const hasAccess = userCtms.some(ctm => ctm.id === ctmId);

    if (!hasAccess) {
      throw new ForbiddenException(
        'User does not have access to the specified CTM. Please contact your administrator.',
      );
    }
  }

  /**
   * Get CTM configuration and validate it exists
   */
  private async getCtmConfig(ctmId: string): Promise<CtmConfig> {
    const ctmConfig = await this.ctmService.getCtmConfig(ctmId);

    if (!ctmConfig) {
      throw new ForbiddenException(
        'CTM configuration not found or incomplete. Please contact your administrator.',
      );
    }

    return ctmConfig;
  }

  /**
   * Get CTM SeqRNG configuration
   */
  private async getCtmSeqrngConfig(ctmId: string): Promise<SeqrngConfig | undefined> {
    return await this.ctmService.getCtmSeqrngConfig(ctmId) || undefined;
  }

  /**
   * Determine key type based on algorithm
   */
  private determineKeyType(algorithm: string): KeyType {
    if (!algorithm) return KeyType.HEX_KEY;

    const algorithmLower = algorithm.toLowerCase();

    if (algorithmLower === 'rsa') {
      return KeyType.HEX_KEY; // RSA keys are treated as hex keys
    }

    return KeyType.HEX_KEY; // Default to hex key for symmetric algorithms
  }
}
