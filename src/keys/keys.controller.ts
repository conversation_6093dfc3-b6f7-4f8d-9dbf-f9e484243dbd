import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { KeysService } from './keys.service';
import { GenerateBytesDto } from './dto/generate-bytes.dto';
import { GenerateKeyDto } from './dto/generate-key.dto';
import { UploadKeyDto } from './dto/upload-key.dto';
import { UploadBatchKeysDto } from './dto/upload-batch-keys.dto';
import { GetKeysDto } from './dto/get-keys.dto';
import { GetCtmKeysDto } from './dto/get-ctm-keys.dto';
import { KeyResponseDto, KeyListResponseDto } from './dto/key-response.dto';
import { CreateKeyVersionDto, CreateKeyVersionResponseDto } from './dto/create-key-version.dto';
import { KeyVersionsResponseDto } from './dto/key-versions-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Keys')
@Controller('keys')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class KeysController {
  constructor(private readonly keysService: KeysService) {}

  @Post('generate/bytes')
  @ApiOperation({ summary: 'Generar bytes aleatorios cuánticos' })
  @ApiResponse({
    status: 201,
    description: 'Bytes aleatorios generados exitosamente',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async generateRandomBytes(
    @Body() generateBytesDto: GenerateBytesDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.generateRandomBytes(userId, generateBytesDto.ctmId, generateBytesDto);
  }

  @Post('generate/hex')
  @ApiOperation({ summary: 'Generar llave hexadecimal cuántica' })
  @ApiResponse({
    status: 201,
    description: 'Llave hexadecimal generada exitosamente',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async generateHexKey(
    @Body() generateKeyDto: GenerateKeyDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.generateHexKey(userId, generateKeyDto.ctmId, generateKeyDto);
  }

  @Post('generate/alphanumeric')
  @ApiOperation({ summary: 'Generar llave alfanumérica cuántica' })
  @ApiResponse({
    status: 201,
    description: 'Llave alfanumérica generada exitosamente',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async generateAlphanumericKey(
    @Body() generateKeyDto: GenerateKeyDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.generateAlphanumericKey(userId, generateKeyDto.ctmId, generateKeyDto);
  }

  @Post('upload-to-ctm')
  @ApiOperation({
    summary: 'Subir llave a CipherTrust Manager',
    description: 'El propietario (owner) se determina automáticamente: usa el nombre de la empresa (en minúsculas y espacios reemplazados por _), o firstName_lastName si no tiene empresa'
  })
  @ApiResponse({
    status: 201,
    description: 'Llave subida exitosamente a CTM',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  @ApiResponse({
    status: 409,
    description: 'La llave ya existe en CTM',
  })
  async uploadKeyToCtm(
    @Body() uploadKeyDto: UploadKeyDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.uploadKeyToCtm(userId, uploadKeyDto.ctmId, uploadKeyDto);
  }

  @Post('upload-batch-to-ctm')
  @ApiOperation({
    summary: 'Subir múltiples llaves a CipherTrust Manager',
    description: 'El propietario (owner) se determina automáticamente: usa el nombre de la empresa (en minúsculas y espacios reemplazados por _), o firstName_lastName si no tiene empresa'
  })
  @ApiResponse({
    status: 201,
    description: 'Llaves subidas exitosamente a CTM',
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async uploadBatchKeysToCtm(
    @Body() uploadBatchKeysDto: UploadBatchKeysDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.uploadBatchKeysToCtm(userId, uploadBatchKeysDto.ctmId, uploadBatchKeysDto);
  }

  @Get('ctm/:keyName/exists')
  @ApiOperation({ summary: 'Verificar si una llave existe en CTM' })
  @ApiParam({
    name: 'keyName',
    description: 'Nombre de la llave a verificar',
    example: 'my-encryption-key-001',
  })
  @ApiQuery({
    name: 'ctmId',
    description: 'ID del CTM a utilizar',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'Estado de existencia de la llave verificado',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async checkKeyExists(
    @Param('keyName') keyName: string,
    @Query('ctmId') ctmId: string,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.checkKeyExists(userId, ctmId, keyName);
  }

  @Get('ctm/auth/token')
  @ApiOperation({ summary: 'Obtener token de autenticación CTM' })
  @ApiQuery({
    name: 'ctmId',
    description: 'ID del CTM a utilizar',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'Token CTM obtenido exitosamente',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async getCtmToken(
    @Query('ctmId') ctmId: string,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.getCtmToken(userId, ctmId);
  }

  //getUserLoggedInKeys
  @Get('user')
  @ApiOperation({ summary: 'Obtener llaves del usuario autenticado' })
  @ApiResponse({
    status: 200,
    description: 'Lista de llaves del usuario autenticado',
    type: KeyListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async getUserLoggedInKeys(
    @Request() req: any,
    @Query() getKeysDto: GetKeysDto,
  ): Promise<KeyListResponseDto> {
    const userId = req.user.id;
    return this.keysService.getUserKeys(userId, getKeysDto);
  } 

  @Get('by-user/:userId')
  @ApiOperation({ summary: 'Obtener llaves del usuario' })
  @ApiResponse({
    status: 200,
    description: 'Lista de llaves del usuario',
    type: KeyListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async getUserKeys(
    @Param('userId') userId: string,
    @Query() getKeysDto: GetKeysDto,
  ): Promise<KeyListResponseDto> {
    return this.keysService.getUserKeys(userId, getKeysDto);
  }

  @Get('by-ctm')
  @ApiOperation({
    summary: 'Obtener llaves por CTM',
    description: 'Obtiene todas las llaves del CTM especificado. El usuario debe tener acceso al CTM.'
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de llaves del CTM',
    type: KeyListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async getCtmKeys(
    @Query() getCtmKeysDto: GetCtmKeysDto,
    @Request() req: any,
  ): Promise<KeyListResponseDto> {
    const userId = req.user.id;
    const { ctmId, ...keysDto } = getCtmKeysDto;
    return this.keysService.getCtmKeys(userId, ctmId, keysDto as GetKeysDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener una llave específica por ID' })
  @ApiParam({
    name: 'id',
    description: 'ID de la llave',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Llave encontrada',
    type: KeyResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Llave no encontrada',
  })
  async getUserKey(
    @Param('id') keyId: string,
    @Request() req: any,
  ): Promise<KeyResponseDto> {
    const userId = req.user.id;
    return this.keysService.getUserKey(userId, keyId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Eliminar una llave por ID' })
  @ApiParam({
    name: 'id',
    description: 'ID de la llave',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Llave eliminada exitosamente',
  })
  @ApiResponse({
    status: 404,
    description: 'Llave no encontrada',
  })
  async deleteUserKey(
    @Param('id') keyId: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    const userId = req.user.id;
    await this.keysService.deleteUserKey(userId, keyId);
    return { message: 'Llave eliminada exitosamente' };
  }

  @Post('ctm/:keyId/versions')
  @ApiOperation({
    summary: 'Crear nueva versión de llave existente',
    description: 'Crea una nueva versión de una llave existente en CTM usando material cuántico de SeQRNG'
  })
  @ApiParam({
    name: 'keyId',
    description: 'ID de la llave existente en CTM',
    example: 'key-12345-abcde',
  })
  @ApiResponse({
    status: 201,
    description: 'Nueva versión de llave creada exitosamente',
    type: CreateKeyVersionResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Parámetros inválidos o llave no encontrada',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async createKeyVersion(
    @Param('keyId') keyId: string,
    @Body() createKeyVersionDto: CreateKeyVersionDto,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.createKeyVersion(
      userId,
      createKeyVersionDto.ctmId,
      keyId,
      createKeyVersionDto.archivePrevious
    );
  }

  @Get('versions/:keyId')
  @ApiOperation({
    summary: 'Obtener todas las versiones de una llave',
    description: 'Obtiene todas las versiones de una llave. Si la llave tiene baseKeyId, usa ese para obtener todo el historial; de lo contrario, usa el ID proporcionado como llave base.'
  })
  @ApiParam({
    name: 'keyId',
    description: 'ID de la llave (puede ser una llave base o una versión)',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Versiones de llave obtenidas exitosamente',
    type: KeyVersionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Llave no encontrada',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene configuración CTM',
  })
  async getKeyVersions(
    @Param('keyId') keyId: string,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.getKeyVersions(userId, keyId);
  }

  @Post('synchronize')
  @ApiOperation({
    summary: 'Sincronizar llaves de CTM',
    description: 'Sincroniza las llaves del ambiente CTM especificado con la base de datos local. Solo guarda llaves que no existen previamente en la base de datos.'
  })
  @ApiResponse({
    status: 201,
    description: 'Sincronización completada exitosamente',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'success' },
        message: { type: 'string', example: 'Synchronized 5 new keys from CTM' },
        data: {
          type: 'object',
          properties: {
            totalCtmKeys: { type: 'number', example: 10 },
            existingInDatabase: { type: 'number', example: 5 },
            newKeysSynchronized: { type: 'number', example: 5 },
            failedSynchronizations: { type: 'number', example: 0 },
            synchronizedKeys: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  ctmKeyId: { type: 'string' },
                  name: { type: 'string' },
                  algorithm: { type: 'string' },
                  status: { type: 'string', example: 'synchronized' }
                }
              }
            },
            failedKeys: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  ctmKeyId: { type: 'string' },
                  name: { type: 'string' },
                  error: { type: 'string' }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Error en la sincronización',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async synchronizeCtmKeys(
    @Body() ctmOperationDto: { ctmId: string },
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.synchronizeCtmKeys(userId, ctmOperationDto.ctmId);
  }

  @Get('ctm/:keyId/info')
  @ApiOperation({
    summary: 'Obtener información de llave específica',
    description: 'Obtiene información detallada de una llave específica desde CTM'
  })
  @ApiParam({
    name: 'keyId',
    description: 'ID de la llave en CTM',
    example: 'key-12345-abcde',
  })
  @ApiQuery({
    name: 'ctmId',
    description: 'ID del CTM a utilizar',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'Información de llave obtenida exitosamente',
  })
  @ApiResponse({
    status: 400,
    description: 'Llave no encontrada',
  })
  @ApiResponse({
    status: 403,
    description: 'Usuario no tiene acceso al CTM especificado',
  })
  async getKeyInfo(
    @Param('keyId') keyId: string,
    @Query('ctmId') ctmId: string,
    @Request() req: any,
  ) {
    const userId = req.user.id;
    return this.keysService.getKeyInfo(userId, ctmId, keyId);
  }
}
