import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Ctm } from '../../ctm/entities/ctm.entity';

export enum KeyType {
  RANDOM_BYTES = 'random_bytes',
  HEX_KEY = 'hex_key',
  ALPHANUMERIC_KEY = 'alphanumeric_key',
}

export enum KeyAlgorithm {
  AES = 'aes',
  ARIA = 'aria',
  RSA = 'rsa',
  HMAC_SHA1 = 'hmac-sha1',
  HMAC_SHA256 = 'hmac-sha256',
  HMAC_SHA384 = 'hmac-sha384',
  HMAC_SHA512 = 'hmac-sha512',
}

export enum KeyStatus {
  GENERATED = 'generated',
  UPLOADED_TO_CTM = 'uploaded_to_ctm',
  FAILED = 'failed',
}

@Entity('keys')
export class Key {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: KeyType,
  })
  type: KeyType;

  @Column({
    type: 'enum',
    enum: KeyAlgorithm,
    nullable: true,
  })
  algorithm: KeyAlgorithm | null;

  @Column({ type: 'int' })
  numBytes: number;

  @Column({
    type: 'enum',
    enum: KeyStatus,
    default: KeyStatus.GENERATED,
  })
  status: KeyStatus;

  @Column({ type: 'varchar', length: 100, nullable: true })
  owner: string | null;

  @Column({ type: 'boolean', default: false })
  exportable: boolean;

  @Column({ type: 'boolean', default: false })
  uploadedToCtm: boolean;

  @Column({ type: 'text', nullable: true })
  ctmKeyName: string | null;

  @Column({ type: 'text', nullable: true })
  ctmKeyId: string | null;

  @Column({ type: 'text', nullable: true })
  entropyReport: string | null; // JSON string

  @Column({ type: 'text', nullable: true })
  errorMessage: string | null;

  // Versioning fields
  @Column({ type: 'uuid', nullable: true })
  baseKeyId: string | null; // ID of the original key (null for base keys)

  @Column({ type: 'int', default: 0 })
  version: number; // Version number (0 for base keys, 1+ for versions)

  @Column({ type: 'boolean', default: true })
  active: boolean; // Whether the key is active (false when archived)

  @Column({ type: 'timestamp', nullable: true })
  archiveDate: Date | null; // Date when the key was archived in CTM

  // Self-referencing relationship for key versions
  @ManyToOne(() => Key, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'baseKeyId' })
  baseKey: Key | null;

  @OneToMany(() => Key, key => key.baseKey)
  versions: Key[];

  // Relación con el usuario (mantener para auditoría)
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'uuid' })
  userId: string;

  // Relación con el CTM
  @ManyToOne(() => Ctm, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ctmId' })
  ctm: Ctm;

  @Column({ type: 'uuid' })
  ctmId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Getters para información adicional
  get isSuccessful(): boolean {
    return this.status !== KeyStatus.FAILED;
  }

  get displayName(): string {
    return this.ctmKeyName || this.name;
  }
}
