import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  OneToMany,
  JoinTable,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Hsm<PERSON>ey } from './hsm-key.entity';

@Entity('hsms')
export class Hsm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'varchar', length: 255 })
  url: string; // URL base del HSM (ej: http://tomcatlinux:8080)

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relación many-to-many con usuarios
  @ManyToMany(() => User, user => user.hsms)
  @JoinTable({
    name: 'user_hsms',
    joinColumn: {
      name: 'hsmId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'userId',
      referencedColumnName: 'id',
    },
  })
  users: User[];

  // Relación one-to-many con llaves HSM
  @OneToMany(() => HsmKey, hsmKey => hsmKey.hsm)
  keys: HsmKey[];

  // Computed property para verificar configuración completa
  get hasCompleteConfiguration(): boolean {
    return !!(this.name && this.url);
  }

  // Computed property para obtener la URL completa de un endpoint
  getEndpointUrl(endpoint: string): string {
    const baseUrl = this.url.endsWith('/') ? this.url.slice(0, -1) : this.url;
    const endpointPath = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${baseUrl}/sq.jm.hsm-0.0.1-SNAPSHOT${endpointPath}`;
  }
}
