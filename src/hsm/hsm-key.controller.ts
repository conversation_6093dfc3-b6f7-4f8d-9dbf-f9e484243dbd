import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  Query,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { HsmKeyService } from './hsm-key.service';
import { CreateHsmKeyDto, HsmKeyResponseDto } from './dto/create-hsm-key.dto';
import { GetHsmKeysDto } from './dto/get-hsm-keys.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@ApiTags('HSM Keys')
@ApiBearerAuth('JWT-auth')
@Controller('hsm-keys')
@UseGuards(JwtAuthGuard)
export class HsmKeyController {
  constructor(private readonly hsmKeyService: HsmKeyService) {}

  @Post()
  @ApiOperation({ summary: 'Crear nueva llave HSM' })
  @ApiResponse({
    status: 201,
    description: 'Llave HSM creada exitosamente',
    type: HsmKeyResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Parámetros inválidos para el tipo de llave' })
  @ApiResponse({ status: 403, description: 'Usuario no tiene acceso al HSM especificado' })
  @ApiResponse({ status: 404, description: 'HSM no encontrado' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  createKey(
    @Request() req: any,
    @Body() createHsmKeyDto: CreateHsmKeyDto,
  ): Promise<HsmKeyResponseDto> {
    const userId = req.user.id;
    return this.hsmKeyService.createKey(userId, createHsmKeyDto);
  }

  @Get()
  @ApiOperation({ summary: 'Obtener llaves HSM del usuario autenticado' })
  @ApiResponse({
    status: 200,
    description: 'Lista de llaves HSM del usuario',
    type: [HsmKeyResponseDto],
  })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  getUserKeys(
    @Request() req: any,
    @Query() getHsmKeysDto: GetHsmKeysDto,
  ): Promise<HsmKeyResponseDto[]> {
    const userId = req.user.id;
    return this.hsmKeyService.findUserKeys(userId, getHsmKeysDto);
  }

  @Get('all')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Obtener todas las llaves HSM (Solo Admin)' })
  @ApiResponse({
    status: 200,
    description: 'Lista de todas las llaves HSM',
    type: [HsmKeyResponseDto],
  })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  getAllKeys(@Query() getHsmKeysDto: GetHsmKeysDto): Promise<HsmKeyResponseDto[]> {
    return this.hsmKeyService.findAllKeys(getHsmKeysDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener llave HSM por ID' })
  @ApiParam({ name: 'id', description: 'ID de la llave HSM', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Llave HSM encontrada',
    type: HsmKeyResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Llave HSM no encontrada' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  findOne(
    @Request() req: any,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<HsmKeyResponseDto> {
    const userId = req.user.role === UserRole.ADMIN ? undefined : req.user.id;
    return this.hsmKeyService.findOne(id, userId);
  }
}
