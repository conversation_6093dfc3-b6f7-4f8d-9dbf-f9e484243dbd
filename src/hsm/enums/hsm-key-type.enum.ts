/**
 * Enum para los tipos de algoritmos soportados por HSM
 * Basado en los endpoints REST del middleware Tomcat
 */
export enum HsmKeyType {
  // Algoritmos simétricos
  AES = 'aes',
  
  // Algoritmos asimétricos
  RSA = 'rsa',
  
  // Algoritmos de curva elíptica
  EC = 'ec',
  
  // Algoritmos post-cuánticos
  KYBER = 'kyberkn',      // Kyber KEM post-cuántico
  DILITHIUM = 'dilithiumkn', // Dilithium firma digital post-cuántica
}

/**
 * Enum para las curvas elípticas soportadas
 */
export enum EccCurveType {
  SECT233K1 = 'sect233k1',
  SECP256R1 = 'secp256r1',
  SECP384R1 = 'secp384r1',
  SECP521R1 = 'secp521r1',
  // Agregar más curvas según sea necesario
}

/**
 * Enum para los tamaños de key soportados
 */
export enum KeySize {
  AES_128 = 128,
  AES_192 = 192,
  AES_256 = 256,
  RSA_2048 = 2048,
  RSA_3072 = 3072,
  RSA_4096 = 4096,
}

/**
 * Enum para el estado de las llaves HSM
 */
export enum HsmKeyStatus {
  PENDING = 'pending',
  CREATED = 'created',
  FAILED = 'failed',
}

/**
 * Enum para los endpoints HSM según el tipo de algoritmo
 */
export enum HsmEndpoint {
  KEYS = '/keys',           // Para RSA y AES
  KEYS_EC = '/keysEC',      // Para curvas elípticas
  KEYS_PQC = '/keysPQC',    // Para algoritmos post-cuánticos
}
