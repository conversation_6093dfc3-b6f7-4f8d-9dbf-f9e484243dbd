import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { HsmService } from './hsm.service';
import { CreateHsmDto } from './dto/create-hsm.dto';
import { UpdateHsmDto } from './dto/update-hsm.dto';
import { GetHsmsDto } from './dto/get-hsms.dto';
import { AssignHsmDto, AssignUsersToHsmDto } from './dto/assign-hsm.dto';
import { HsmResponseDto } from './dto/hsm-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@ApiTags('HSM Management')
@ApiBearerAuth('JWT-auth')
@Controller('hsms')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class HsmController {
  constructor(private readonly hsmService: HsmService) {}

  @Post()
  @ApiOperation({ summary: 'Crear nuevo HSM (Solo Admin)' })
  @ApiResponse({
    status: 201,
    description: 'HSM creado exitosamente',
    type: HsmResponseDto,
  })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  @ApiResponse({ status: 409, description: 'El nombre del HSM ya existe' })
  create(@Body() createHsmDto: CreateHsmDto): Promise<HsmResponseDto> {
    return this.hsmService.create(createHsmDto);
  }

  @Get()
  @ApiOperation({ summary: 'Obtener todos los HSMs (Solo Admin)' })
  @ApiResponse({
    status: 200,
    description: 'Lista de HSMs obtenida exitosamente',
  })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  findAll(@Query() getHsmsDto: GetHsmsDto) {
    return this.hsmService.findAll(getHsmsDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener HSM por ID (Solo Admin)' })
  @ApiParam({ name: 'id', description: 'ID del HSM', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'HSM encontrado',
    type: HsmResponseDto,
  })
  @ApiResponse({ status: 404, description: 'HSM no encontrado' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<HsmResponseDto> {
    return this.hsmService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Actualizar HSM (Solo Admin)' })
  @ApiParam({ name: 'id', description: 'ID del HSM', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'HSM actualizado exitosamente',
    type: HsmResponseDto,
  })
  @ApiResponse({ status: 404, description: 'HSM no encontrado' })
  @ApiResponse({ status: 409, description: 'El nombre del HSM ya existe' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateHsmDto: UpdateHsmDto,
  ): Promise<HsmResponseDto> {
    return this.hsmService.update(id, updateHsmDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Eliminar HSM (Solo Admin)' })
  @ApiParam({ name: 'id', description: 'ID del HSM', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'HSM eliminado exitosamente',
  })
  @ApiResponse({ status: 404, description: 'HSM no encontrado' })
  @ApiResponse({ status: 400, description: 'No se puede eliminar HSM con usuarios o llaves asignadas' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.hsmService.remove(id);
  }

  @Post(':id/assign-users')
  @ApiOperation({ summary: 'Asignar usuarios a un HSM (Solo Admin)' })
  @ApiParam({ name: 'id', description: 'ID del HSM', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Usuarios asignados exitosamente al HSM',
  })
  @ApiResponse({ status: 404, description: 'HSM no encontrado' })
  @ApiResponse({ status: 400, description: 'Uno o más usuarios no encontrados o inactivos' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  async assignUsersToHsm(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignUsersDto: AssignUsersToHsmDto,
  ): Promise<{ message: string }> {
    await this.hsmService.assignUsersToHsm(id, assignUsersDto);
    return { message: 'Usuarios asignados exitosamente al HSM' };
  }

  @Post('assign-to-user/:userId')
  @ApiOperation({ summary: 'Asignar HSMs a un usuario (Solo Admin)' })
  @ApiParam({ name: 'userId', description: 'ID del usuario', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'HSMs asignados exitosamente al usuario',
  })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  @ApiResponse({ status: 400, description: 'Uno o más HSMs no encontrados o inactivos' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  async assignHsmsToUser(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() assignHsmDto: AssignHsmDto,
  ): Promise<{ message: string }> {
    await this.hsmService.assignHsmsToUser(userId, assignHsmDto);
    return { message: 'HSMs asignados exitosamente al usuario' };
  }

  @Delete(':id/users/:userId')
  @ApiOperation({ summary: 'Remover usuario de un HSM (Solo Admin)' })
  @ApiParam({ name: 'id', description: 'ID del HSM', type: 'string' })
  @ApiParam({ name: 'userId', description: 'ID del usuario', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Usuario removido exitosamente del HSM',
  })
  @ApiResponse({ status: 404, description: 'HSM no encontrado' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  async removeUserFromHsm(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('userId', ParseUUIDPipe) userId: string,
  ): Promise<{ message: string }> {
    await this.hsmService.removeUserFromHsm(id, userId);
    return { message: 'Usuario removido exitosamente del HSM' };
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Obtener HSMs asignados a un usuario (Solo Admin)' })
  @ApiParam({ name: 'userId', description: 'ID del usuario', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Lista de HSMs del usuario',
    type: [HsmResponseDto],
  })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  getUserHsms(@Param('userId', ParseUUIDPipe) userId: string): Promise<HsmResponseDto[]> {
    return this.hsmService.getUserHsms(userId);
  }
}
