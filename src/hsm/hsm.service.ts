import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Hsm } from './entities/hsm.entity';
import { User } from '../users/entities/user.entity';
import { CreateHsmDto } from './dto/create-hsm.dto';
import { UpdateHsmDto } from './dto/update-hsm.dto';
import { GetHsmsDto } from './dto/get-hsms.dto';
import { AssignHsmDto, AssignUsersToHsmDto } from './dto/assign-hsm.dto';
import { HsmResponseDto } from './dto/hsm-response.dto';
import { plainToClass } from 'class-transformer';

@Injectable()
export class HsmService {
  private readonly logger = new Logger(HsmService.name);

  constructor(
    @InjectRepository(Hsm)
    private hsmRepository: Repository<Hsm>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async create(createHsmDto: CreateHsmDto): Promise<HsmResponseDto> {
    this.logger.log(`Creating HSM: ${createHsmDto.name}`);

    // Check if HSM name already exists
    const existingHsm = await this.hsmRepository.findOne({
      where: { name: createHsmDto.name },
    });

    if (existingHsm) {
      throw new ConflictException('HSM name already exists');
    }

    const hsm = this.hsmRepository.create(createHsmDto);
    const savedHsm = await this.hsmRepository.save(hsm);

    this.logger.log(`HSM created successfully: ${savedHsm.id}`);
    return plainToClass(HsmResponseDto, savedHsm, { excludeExtraneousValues: true });
  }

  async findAll(getHsmsDto: GetHsmsDto): Promise<HsmResponseDto[]> {
    this.logger.log('Fetching all HSMs with filters');

    const queryBuilder = this.hsmRepository.createQueryBuilder('hsm');

    if (getHsmsDto.name) {
      queryBuilder.andWhere('hsm.name ILIKE :name', { name: `%${getHsmsDto.name}%` });
    }

    if (getHsmsDto.isActive !== undefined) {
      queryBuilder.andWhere('hsm.isActive = :isActive', { isActive: getHsmsDto.isActive });
    }

    queryBuilder.orderBy('hsm.createdAt', 'DESC');

    const hsms = await queryBuilder.getMany();
    return hsms.map(hsm => plainToClass(HsmResponseDto, hsm, { excludeExtraneousValues: true }));
  }

  async findOne(id: string): Promise<HsmResponseDto> {
    this.logger.log(`Fetching HSM: ${id}`);

    const hsm = await this.hsmRepository.findOne({
      where: { id },
    });

    if (!hsm) {
      throw new NotFoundException('HSM not found');
    }

    return plainToClass(HsmResponseDto, hsm, { excludeExtraneousValues: true });
  }

  async update(id: string, updateHsmDto: UpdateHsmDto): Promise<HsmResponseDto> {
    this.logger.log(`Updating HSM: ${id}`);

    const hsm = await this.hsmRepository.findOne({
      where: { id },
    });

    if (!hsm) {
      throw new NotFoundException('HSM not found');
    }

    // Check if name is being updated and already exists
    if (updateHsmDto.name && updateHsmDto.name !== hsm.name) {
      const existingHsm = await this.hsmRepository.findOne({
        where: { name: updateHsmDto.name },
      });

      if (existingHsm) {
        throw new ConflictException('HSM name already exists');
      }
    }

    Object.assign(hsm, updateHsmDto);
    const updatedHsm = await this.hsmRepository.save(hsm);

    this.logger.log(`HSM updated successfully: ${updatedHsm.id}`);
    return plainToClass(HsmResponseDto, updatedHsm, { excludeExtraneousValues: true });
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Removing HSM: ${id}`);

    const hsm = await this.hsmRepository.findOne({
      where: { id },
      relations: ['users', 'keys'],
    });

    if (!hsm) {
      throw new NotFoundException('HSM not found');
    }

    if (hsm.users && hsm.users.length > 0) {
      throw new BadRequestException('Cannot delete HSM with assigned users. Remove user assignments first.');
    }

    if (hsm.keys && hsm.keys.length > 0) {
      throw new BadRequestException('Cannot delete HSM with existing keys. Remove keys first.');
    }

    await this.hsmRepository.remove(hsm);
    this.logger.log(`HSM removed successfully: ${id}`);
  }

  async assignUsersToHsm(hsmId: string, assignUsersDto: AssignUsersToHsmDto): Promise<void> {
    this.logger.log(`Assigning users to HSM: ${hsmId}`);

    const hsm = await this.hsmRepository.findOne({
      where: { id: hsmId },
      relations: ['users'],
    });

    if (!hsm) {
      throw new NotFoundException('HSM not found');
    }

    const users = await this.userRepository.findBy({
      id: In(assignUsersDto.userIds),
    });

    if (users.length !== assignUsersDto.userIds.length) {
      throw new BadRequestException('One or more users not found');
    }

    // Check if users are active
    const inactiveUsers = users.filter(user => !user.isActive);
    if (inactiveUsers.length > 0) {
      throw new BadRequestException('Cannot assign inactive users to HSM');
    }

    hsm.users = users;
    await this.hsmRepository.save(hsm);

    this.logger.log(`Users assigned to HSM successfully: ${hsmId}`);
  }

  async assignHsmsToUser(userId: string, assignHsmDto: AssignHsmDto): Promise<void> {
    this.logger.log(`Assigning HSMs to user: ${userId}`);

    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['hsms'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.isActive) {
      throw new BadRequestException('Cannot assign HSMs to inactive user');
    }

    const hsms = await this.hsmRepository.findBy({
      id: In(assignHsmDto.hsmIds),
    });

    if (hsms.length !== assignHsmDto.hsmIds.length) {
      throw new BadRequestException('One or more HSMs not found');
    }

    // Check if HSMs are active
    const inactiveHsms = hsms.filter(hsm => !hsm.isActive);
    if (inactiveHsms.length > 0) {
      throw new BadRequestException('Cannot assign inactive HSMs to user');
    }

    user.hsms = hsms;
    await this.userRepository.save(user);

    this.logger.log(`HSMs assigned to user successfully: ${userId}`);
  }

  async removeUserFromHsm(hsmId: string, userId: string): Promise<void> {
    this.logger.log(`Removing user ${userId} from HSM: ${hsmId}`);

    const hsm = await this.hsmRepository.findOne({
      where: { id: hsmId },
      relations: ['users'],
    });

    if (!hsm) {
      throw new NotFoundException('HSM not found');
    }

    hsm.users = hsm.users.filter(user => user.id !== userId);
    await this.hsmRepository.save(hsm);

    this.logger.log(`User removed from HSM successfully: ${hsmId}`);
  }

  async getUserHsms(userId: string): Promise<HsmResponseDto[]> {
    this.logger.log(`Fetching HSMs for user: ${userId}`);

    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['hsms'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const activeHsms = user.hsms.filter(hsm => hsm.isActive);
    return activeHsms.map(hsm => plainToClass(HsmResponseDto, hsm, { excludeExtraneousValues: true }));
  }
}
