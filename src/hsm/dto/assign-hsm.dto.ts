import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsArray, ArrayNotEmpty } from 'class-validator';

export class AssignHsmDto {
  @ApiProperty({
    example: ['uuid-1', 'uuid-2'],
    description: 'Array de IDs de HSMs a asignar al usuario',
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('4', { each: true })
  hsmIds: string[];
}

export class AssignUsersToHsmDto {
  @ApiProperty({
    example: ['user-uuid-1', 'user-uuid-2'],
    description: 'Array de IDs de usuarios a asignar al HSM',
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('4', { each: true })
  userIds: string[];
}
