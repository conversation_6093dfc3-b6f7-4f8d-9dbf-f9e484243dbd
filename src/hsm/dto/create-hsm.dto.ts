import { ApiProperty } from '@nestjs/swagger';
import { IsString, MinLength, <PERSON>Length, IsOptional, IsUrl } from 'class-validator';

export class CreateHsmDto {
  @ApiProperty({
    example: 'hsm_production_01',
    description: 'Nombre único del HSM',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    example: 'HSM de producción para el equipo de desarrollo',
    description: 'Descripción del HSM',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    example: 'http://tomcatlinux:8080',
    description: 'URL base del HSM (sin el path del middleware)',
  })
  @IsString()
  @MaxLength(255)
  @IsUrl({}, { message: 'La URL debe ser válida' })
  url: string;
}
