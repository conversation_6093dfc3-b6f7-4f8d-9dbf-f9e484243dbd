import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CtmService } from './ctm.service';
import { CtmController } from './ctm.controller';
import { UserCtmController } from './user-ctm.controller';
import { Ctm } from './entities/ctm.entity';
import { User } from '../users/entities/user.entity';
import { EncryptionService } from '../common/services/encryption.service';

@Module({
  imports: [TypeOrmModule.forFeature([Ctm, User])],
  controllers: [CtmController, UserCtmController],
  providers: [CtmService, EncryptionService],
  exports: [CtmService],
})
export class CtmModule {}
